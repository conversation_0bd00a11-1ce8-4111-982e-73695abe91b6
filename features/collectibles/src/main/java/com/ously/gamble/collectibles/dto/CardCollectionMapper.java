package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
public class CardCollectionMapper {

    public CardCollectionDto.CardCollectionResponse toResponse(CardCollection collection) {
        return new CardCollectionDto.CardCollectionResponse(
                collection.getId(),
                collection.getName(),
                collection.getStartDate(),
                collection.getEndDate(),
                collection.getStatus(),
                collection.getSortOrder(),
                collection.getCards().stream()
                        .sorted((c1, c2) -> {
                            int sortOrderCompare = Byte.compare(c1.getSortOrder(), c2.getSortOrder());
                            return sortOrderCompare != 0 ? sortOrderCompare : c1.getId().compareTo(c2.getId());
                        })
                        .map(this::toCardResponse)
                        .collect(Collectors.toList()),
                collection.getRewards().stream()
                        .sorted((r1, r2) -> r1.getId().compareTo(r2.getId()))
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

//    public CardCollectionDto.CardCollectionSummaryResponse toSummaryResponse(CardCollection collection) {
//        return new CardCollectionDto.CardCollectionSummaryResponse(
//                collection.getId(),
//                collection.getName(),
//                collection.getDescription(),
//                collection.getStatus(),
//                collection.getStartDate(),
//                collection.getEndDate(),
//                collection.getCards().size(),
//                collection.getRewards().size(),
//                collection.isActive(),
//                collection.isExpired()
//        );
//    }
//
    public CardCollectionDto.CardResponse toCardResponse(Card card) {
        return new CardCollectionDto.CardResponse(
                card.getId(),
                card.getName(),
                card.getImageUrl(),
                card.getRarityLevel(),
                card.getStartDate(),
                card.getEndDate(),
                card.getStatus(),
                card.getSortOrder(),
                card.getRewards().stream()
                        .sorted((r1, r2) -> r1.getId().compareTo(r2.getId()))
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardCollectionDto.RewardResponse toRewardResponse(Reward reward) {
        String targetType = reward.isForCollection() ? "COLLECTION" : "CARD";
        Integer targetId = reward.getTargetId();
        String targetName = reward.getTargetName();

        return new CardCollectionDto.RewardResponse(
                reward.getId(),
                reward.getRewardType(),
                reward.getMilestonePercentage(),
                reward.getRewardData(),
                targetType,
                targetId,
                targetName
        );
    }
//
//    public List<CardCollectionDto.CardCollectionSummaryResponse> toSummaryResponseList(List<CardCollection> collections) {
//        return collections.stream()
//                .map(this::toSummaryResponse)
//                .collect(Collectors.toList());
//    }
//
//    public List<CardCollectionDto.CardResponse> toCardResponseList(List<Card> cards) {
//        return cards.stream()
//                .map(this::toCardResponse)
//                .collect(Collectors.toList());
//    }
//
//    public List<CardCollectionDto.CardResponse> toCardResponseList(List<Card> cards) {
//        return cards.stream()
//                .map(this::toCardResponse)
//                .collect(Collectors.toList());
//    }
}
